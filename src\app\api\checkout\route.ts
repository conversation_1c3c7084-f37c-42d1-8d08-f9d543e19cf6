import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { initFirebase } from '../../../../firebaseConfig';
import { getAuth } from 'firebase/auth';
import { createTransaction, createEscrowTransaction, updateTransaction } from '@/services/transactionService';
import { getUserById } from '@/services/usersServices';

console.log("STRIPE_SECRET_KEY loaded as:", process.env.STRIPE_SECRET_KEY);

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      userEmail,
      amount = 1000,
      currency = 'usd',
      productName = 'Demo Product',
      productDescription,
      // Escrow-specific fields
      isEscrow = false,
      sellerId,
      sellerEmail,
      sellerStripeAccountId,
      orderId
    } = await request.json();

    // Validate required fields
    if (!userId || !userEmail) {
      return NextResponse.json({ error: 'User ID and email are required' }, { status: 400 });
    }

    // Additional validation for escrow payments
    if (isEscrow) {
      if (!sellerId || !sellerEmail || !sellerStripeAccountId || !orderId) {
        return NextResponse.json({
          error: 'Escrow payments require: sellerId, sellerEmail, sellerStripeAccountId, orderId'
        }, { status: 400 });
      }

      // Validate seller's Stripe account
      try {
        const account = await stripe.accounts.retrieve(sellerStripeAccountId);
        if (!account.charges_enabled || !account.payouts_enabled) {
          return NextResponse.json({
            error: 'Seller account is not fully set up for payments'
          }, { status: 400 });
        }
      } catch (error) {
        return NextResponse.json({
          error: 'Invalid seller Stripe account'
        }, { status: 400 });
      }
    }

    // Get user details from Firebase
    let userDetails;
    try {
      const userResult = await getUserById(userId);
      if (userResult.success) {
        userDetails = userResult.user;
      }
    } catch (error) {
      console.log('Could not fetch user details:', error);
    }

    // Get the origin, but fix 0.0.0.0 to localhost for development
    const origin = request.nextUrl.origin.replace('0.0.0.0', 'localhost');

    // Create or get Stripe customer
    let customer;
    try {
      const existingCustomers = await stripe.customers.list({
        email: userEmail,
        limit: 1,
      });

      if (existingCustomers.data.length > 0) {
        customer = existingCustomers.data[0];
      } else {
        customer = await stripe.customers.create({
          email: userEmail,
          name: userDetails?.profile_name || undefined,
          metadata: {
            userId: userId,
          },
        });
      }
    } catch (error) {
      console.error('Error creating/getting customer:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return NextResponse.json({
        error: 'Failed to create customer',
        details: errorMessage,
        userId,
        userEmail
      }, { status: 500 });
    }

    // Create transaction record in Firebase
    let transactionResult;

    if (isEscrow) {
      // Create escrow transaction
      transactionResult = await createEscrowTransaction({
        userId,
        userEmail,
        sellerId: sellerId!,
        sellerEmail: sellerEmail!,
        sellerStripeAccountId: sellerStripeAccountId!,
        orderId: orderId!,
        amount,
        currency,
        productName,
        productDescription,
        stripeCustomerId: customer.id,
        metadata: {
          origin,
          userAgent: request.headers.get('user-agent'),
        },
      });
    } else {
      // Create regular transaction
      transactionResult = await createTransaction({
        userId,
        userEmail,
        stripeCustomerId: customer.id,
        amount,
        currency,
        status: 'pending',
        productName,
        productDescription,
        metadata: {
          origin,
          userAgent: request.headers.get('user-agent'),
        },
      });
    }

    if (!transactionResult.success) {
      return NextResponse.json({ error: 'Failed to create transaction record' }, { status: 500 });
    }

    const session = await stripe.checkout.sessions.create({
      ui_mode: 'embedded',
      mode: 'payment',
      payment_method_types: ['card'],
      customer: customer.id,
      line_items: [
        {
          price_data: {
            currency,
            product_data: {
              name: productName,
              description: productDescription,
            },
            unit_amount: amount,
          },
          quantity: 1,
        },
      ],
      return_url: isEscrow
        ? `${origin}/payment-return?session_id={CHECKOUT_SESSION_ID}&transaction_id=${transactionResult.transactionId}&order_id=${orderId}`
        : `${origin}/payment-return?session_id={CHECKOUT_SESSION_ID}&transaction_id=${transactionResult.transactionId}`,
      metadata: {
        userId,
        transactionId: transactionResult.transactionId || '',
        ...(isEscrow && {
          isEscrow: 'true',
          sellerId,
          orderId
        })
      },
    });

    // Update transaction with session ID
    if (transactionResult.transactionId) {
      await updateTransaction(transactionResult.transactionId, {
        stripeSessionId: session.id,
        metadata: {
          origin,
          userAgent: request.headers.get('user-agent'),
          sessionCreated: new Date().toISOString(),
        },
      });
    }

    return NextResponse.json({
      clientSecret: session.client_secret,
      sessionId: session.id,
      customerId: customer.id,
      transactionId: transactionResult.transactionId,
      ...(isEscrow && {
        isEscrow: true,
        orderId,
        sellerId
      })
    });
  } catch (error) {
    console.error('Checkout error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
