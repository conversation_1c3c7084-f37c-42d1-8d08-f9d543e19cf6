'use client';

import React, { useCallback } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  EmbeddedCheckoutProvider,
  EmbeddedCheckout,
} from '@stripe/react-stripe-js';

// Make sure to call `loadStripe` outside of a component's render to avoid
// recreating the `Stripe` object on every render.
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface EmbeddedCheckoutComponentProps {
  userId: string;
  userEmail: string;
  amount?: number;
  currency?: string;
  productName?: string;
  productDescription?: string;
  isEscrow?: boolean;
  sellerId?: string;
  sellerEmail?: string;
  sellerStripeAccountId?: string;
  orderId?: string;
  onError?: (error: string) => void;
  onLoading?: (loading: boolean) => void;
}

export default function EmbeddedCheckoutComponent({
  userId,
  userEmail,
  amount = 1000,
  currency = 'usd',
  productName = 'Demo Product',
  productDescription,
  isEscrow = false,
  sellerId,
  sellerEmail,
  sellerStripeAccountId,
  orderId,
  onError,
  onLoading,
}: EmbeddedCheckoutComponentProps) {
  const fetchClientSecret = useCallback(async () => {
    if (onLoading) onLoading(true);

    try {
      // Use escrow API if this is an escrow payment, otherwise use regular embedded checkout
      const apiEndpoint = isEscrow ? '/api/escrow/create' : '/api/checkout-embedded';

      const response = await fetch(apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userEmail,
          amount,
          currency,
          productName,
          productDescription,
          isEscrow,
          sellerId,
          sellerEmail,
          sellerStripeAccountId,
          orderId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      if (onLoading) onLoading(false);
      return data.clientSecret;
    } catch (error) {
      if (onLoading) onLoading(false);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (onError) onError(errorMessage);
      throw error;
    }
  }, [
    userId,
    userEmail,
    amount,
    currency,
    productName,
    productDescription,
    isEscrow,
    sellerId,
    sellerEmail,
    sellerStripeAccountId,
    orderId,
    onError,
    onLoading,
  ]);

  const options = { fetchClientSecret };

  return (
    <div id="checkout">
      <EmbeddedCheckoutProvider stripe={stripePromise} options={options}>
        <EmbeddedCheckout />
      </EmbeddedCheckoutProvider>
    </div>
  );
}
