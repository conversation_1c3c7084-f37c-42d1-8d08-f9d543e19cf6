import { useState } from 'react';
import EmbeddedCheckoutComponent from '@/components/stripe/EmbeddedCheckout';

export default function BuyFromSeller() {
  const [showCheckout, setShowCheckout] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const handlePaySeller = () => {
    setShowCheckout(true);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setLoading(false);
  };

  const handleLoading = (isLoading: boolean) => {
    setLoading(isLoading);
  };

  if (showCheckout) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Complete Your Purchase</h1>
          <p className="text-gray-600">Pay Seller $10.00</p>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {loading && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-blue-600">Loading checkout...</p>
          </div>
        )}

        <EmbeddedCheckoutComponent
          userId="demo_user_123"
          userEmail="<EMAIL>"
          amount={1000}
          currency="usd"
          productName="Demo Product from Seller"
          productDescription="Test product purchase from seller"
          onError={handleError}
          onLoading={handleLoading}
        />

        <div className="mt-6">
          <button
            onClick={() => setShowCheckout(false)}
            className="text-gray-600 hover:text-gray-800 underline"
          >
            ← Back to product
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-md mx-auto p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-4">Buy from Seller</h1>
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
        <h2 className="text-lg font-semibold mb-2">Demo Product</h2>
        <p className="text-gray-600 mb-4">Test product from seller</p>
        <p className="text-2xl font-bold text-green-600 mb-6">$10.00</p>

        <button
          onClick={handlePaySeller}
          disabled={loading}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-4 rounded-md transition-colors"
        >
          {loading ? "Loading..." : "Buy Now"}
        </button>
      </div>
    </div>
  );
}
