import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { createEscrowTransaction } from "@/services/transactionService";
import { getUserById } from "@/services/usersServices";

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request: NextRequest) {
  try {
    const {
      userId,
      userEmail,
      sellerId,
      sellerEmail,
      sellerStripeAccountId,
      orderId,
      amount,
      currency = "usd",
      productName = "Service Order",
      productDescription,
      isEscrow = "true", // This endpoint is specifically for escrow
    } = await request.json();

    // Validate required fields for escrow
    if (
      !userId ||
      !userEmail ||
      !sellerId ||
      !sellerEmail ||
      !sellerStripeAccountId ||
      !orderId ||
      !amount
    ) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: userId, userEmail, sellerId, sellerEmail, sellerStripeAccountId, orderId, amount",
        },
        { status: 400 }
      );
    }

    // Validate seller's Stripe account
    try {
      const account = await stripe.accounts.retrieve(sellerStripeAccountId);
      if (!account.charges_enabled || !account.payouts_enabled) {
        return NextResponse.json(
          {
            error: "Seller account is not fully set up for payments",
          },
          { status: 400 }
        );
      }
    } catch (error) {
      return NextResponse.json(
        {
          error: "Invalid seller Stripe account",
        },
        { status: 400 }
      );
    }

    // Get user details from Firebase
    let userDetails;
    try {
      const userResult = await getUserById(userId);
      if (userResult.success) {
        userDetails = userResult.user;
      }
    } catch (error) {
      console.log("Could not fetch user details:", error);
    }

    // Get or create customer
    let customer;
    try {
      const customers = await stripe.customers.list({
        email: userEmail,
        limit: 1,
      });

      if (customers.data.length > 0) {
        customer = customers.data[0];
        console.log("Found existing customer:", customer.id);
      } else {
        console.log("Creating new customer for user:", userId);
        customer = await stripe.customers.create({
          email: userEmail,
          name: userDetails?.profile_name || undefined,
          metadata: {
            firebaseUid: userId,
          },
        });
        console.log("Created new customer:", customer.id);
      }
    } catch (error) {
      console.error("Error creating customer:", error);
      // If customer with this ID already exists, retrieve it
      try {
        console.log("Attempting to find customer by Firebase UID:", userId);
        // Try to find customer by Firebase UID in metadata
        const existingCustomers = await stripe.customers.list({
          limit: 100,
        });

        const foundCustomer = existingCustomers.data.find(
          (c) => c.metadata?.firebaseUid === userId
        );
        if (foundCustomer) {
          customer = foundCustomer;
          console.log("Found customer by Firebase UID:", customer.id);
        } else {
          throw new Error("No customer found with this Firebase UID");
        }
      } catch (retrieveError) {
        console.error("Error retrieving customer:", retrieveError);
        const errorMessage =
          retrieveError instanceof Error ? retrieveError.message : "Unknown error";
        return NextResponse.json(
          {
            error: "Failed to create or retrieve customer",
            details: errorMessage,
            userId,
            userEmail,
          },
          { status: 500 }
        );
      }
    }

    // Create escrow transaction record
    console.log("Creating escrow transaction with data:", {
      userId,
      userEmail,
      sellerId,
      sellerEmail,
      sellerStripeAccountId,
      orderId,
      amount: parseInt(amount.toString()),
      currency,
      productName,
    });

    const transactionResult = await createEscrowTransaction({
      userId,
      userEmail,
      sellerId,
      sellerEmail,
      sellerStripeAccountId,
      orderId,
      amount: parseInt(amount.toString()),
      currency,
      productName,
      productDescription,
      stripeCustomerId: customer.id,
      metadata: {
        origin: request.headers.get("origin"),
        userAgent: request.headers.get("user-agent"),
      },
    });

    if (!transactionResult.success) {
      console.error("Failed to create escrow transaction:", transactionResult.error);
      return NextResponse.json(
        {
          error: "Failed to create escrow transaction record",
          details: transactionResult.error,
          orderId,
          sellerId,
        },
        { status: 500 }
      );
    }

    // Create Stripe checkout session with automatic capture for escrow
    const origin = request.headers.get("origin") || "http://localhost:3000";
    const session = await stripe.checkout.sessions.create({
      ui_mode: "embedded",
      mode: "payment",
      payment_method_types: ["card"],
      customer: customer.id,
      payment_intent_data: {
        capture_method: "automatic", // Capture payment immediately and hold in Stripe for escrow
        metadata: {
          userId,
          sellerId,
          orderId,
          transactionId: transactionResult.transactionId || "",
          isEscrow: "true",
        },
      },
      line_items: [
        {
          price_data: {
            currency,
            product_data: {
              name: productName,
              description: productDescription,
            },
            unit_amount: parseInt(amount.toString()),
          },
          quantity: 1,
        },
      ],
      return_url: `${origin}/payment-return?session_id={CHECKOUT_SESSION_ID}&transaction_id=${transactionResult.transactionId}&order_id=${orderId}`,
      metadata: {
        userId,
        sellerId,
        orderId,
        transactionId: transactionResult.transactionId || "",
        isEscrow: "true",
      },
    });

    // Update transaction with session ID
    if (transactionResult.transactionId) {
      const { updateTransaction } = await import("@/services/transactionService");
      await updateTransaction(transactionResult.transactionId, {
        stripeSessionId: session.id,
        metadata: {
          origin,
          userAgent: request.headers.get("user-agent"),
          sessionCreated: new Date().toISOString(),
        },
      });
    }

    return NextResponse.json({
      clientSecret: session.client_secret,
      sessionId: session.id,
      customerId: customer.id,
      transactionId: transactionResult.transactionId,
      isEscrow: true,
      orderId,
      sellerId,
      message: "Escrow checkout session created successfully",
    });
  } catch (error) {
    console.error("Escrow checkout error:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    const errorStack = error instanceof Error ? error.stack : undefined;

    return NextResponse.json(
      {
        error: "Escrow checkout failed",
        message: errorMessage,
        stack: process.env.NODE_ENV === "development" ? errorStack : undefined,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
